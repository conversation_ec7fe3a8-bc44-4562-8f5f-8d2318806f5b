from fastapi import FastAPI
# main.py
mood_items = []
mood_counter = 0

app = FastAPI()

@app.post("/moods")
def create_mood(item: dict): # 注意这里是 dict
    """
    获取指定用户的所有心情记录。
    
    - **user_name**: 要查询的用户名。
    """
    global mood_counter
    mood_counter += 1
    
    # 这是一个“定时炸弹”
    new_mood = {
        "id": mood_counter,
        "user": item["user"], # 如果请求里没有"user"，这里会崩溃！
        "mood": item["mood"], # 如果请求里没有"mood"，这里也会崩溃！
    }
    mood_items.append(new_mood)
    return new_mood

